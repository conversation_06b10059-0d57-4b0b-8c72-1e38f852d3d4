.channel-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.channel-page__header {
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  color: #6c757d;
}

.breadcrumb-item {
  color: #007bff;
  text-decoration: none;
  cursor: pointer;
}

.breadcrumb-item.current {
  color: #6c757d;
  cursor: default;
}

.breadcrumb-separator {
  margin: 0 8px;
  color: #6c757d;
}

.channel-page__header h2 {
  margin: 0 0 8px 0;
  color: #1a1a1a;
  font-size: 28px;
  font-weight: 600;
}

.channel-page__header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.channel-page__content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.channel-page__section {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.channel-page__section h3 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
  font-size: 20px;
  font-weight: 600;
}

.channel-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.info-label {
  font-weight: 500;
  color: #495057;
}

.info-value {
  font-weight: 600;
  color: #212529;
}

.status-active {
  color: #28a745 !important;
}

.waveform-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.waveform-display {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.waveform-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #dee2e6;
  background: #f8f9fa;
  border-radius: 6px 6px 0 0;
}

.waveform-title {
  font-weight: 600;
  color: #495057;
}

.waveform-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  padding: 6px 12px;
  border: 1px solid #007bff;
  background: #fff;
  color: #007bff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.control-btn:hover {
  background: #007bff;
  color: #fff;
}

.waveform-chart {
  padding: 40px;
  text-align: center;
  color: #6c757d;
  font-size: 18px;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.waveform-placeholder {
  margin-top: 20px;
  width: 100%;
  max-width: 400px;
}

.wave-line {
  height: 2px;
  background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
  margin: 8px 0;
  border-radius: 1px;
  animation: wave 2s ease-in-out infinite;
}

.wave-line:nth-child(2) {
  animation-delay: 0.3s;
}

.wave-line:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes wave {
  0%, 100% { transform: scaleX(1); }
  50% { transform: scaleX(0.8); }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 24px;
  margin-right: 16px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #007bff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #6c757d;
}

.history-table {
  overflow-x: auto;
}

.history-table table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
}

.history-table th,
.history-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.history-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.history-table tr:hover {
  background: #f8f9fa;
}

.status-normal {
  color: #28a745;
  font-weight: 500;
}

.status-warning {
  color: #ffc107;
  font-weight: 500;
}

.status-error {
  color: #dc3545;
  font-weight: 500;
}

@media (max-width: 768px) {
  .channel-page {
    padding: 16px;
  }
  
  .channel-info-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .waveform-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .waveform-controls {
    width: 100%;
    justify-content: flex-end;
  }
  
  .breadcrumb {
    flex-wrap: wrap;
  }
}
