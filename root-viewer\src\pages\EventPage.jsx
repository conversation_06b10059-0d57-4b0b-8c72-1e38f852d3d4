import { useParams } from 'react-router-dom';
import './EventPage.css';

const EventPage = () => {
  const { category, eventId } = useParams();

  return (
    <div className="event-page">
      <div className="event-page__header">
        <h2>{category} - {eventId}</h2>
        <p>这里是{category}下{eventId}事件的详细监测内容。</p>
      </div>
      
      <div className="event-page__content">
        <div className="event-page__section">
          <h3>事件信息</h3>
          <div className="event-info-grid">
            <div className="info-item">
              <span className="info-label">事件类别:</span>
              <span className="info-value">{category}</span>
            </div>
            <div className="info-item">
              <span className="info-label">事件ID:</span>
              <span className="info-value">{eventId}</span>
            </div>
            <div className="info-item">
              <span className="info-label">状态:</span>
              <span className="info-value status-active">活跃</span>
            </div>
            <div className="info-item">
              <span className="info-label">创建时间:</span>
              <span className="info-value">2024-01-15 14:23:01</span>
            </div>
          </div>
        </div>

        <div className="event-page__section">
          <h3>数据流监测</h3>
          <div className="data-stream-container">
            <div className="data-stream-output">
              <div className="stream-line">
                <span className="timestamp">[2024-01-15 14:23:01]</span>
                <span className="data-type">INFO</span>
                <span className="message">事件 {eventId} 数据流连接已建立</span>
              </div>
              <div className="stream-line">
                <span className="timestamp">[2024-01-15 14:23:02]</span>
                <span className="data-type">DATA</span>
                <span className="message">接收数据包: {"{"}"event": "{eventId}", "value": 42.5, "status": "normal"{"}"}</span>
              </div>
              <div className="stream-line">
                <span className="timestamp">[2024-01-15 14:23:03]</span>
                <span className="data-type">DATA</span>
                <span className="message">接收数据包: {"{"}"event": "{eventId}", "value": 38.2, "status": "normal"{"}"}</span>
              </div>
              <div className="stream-line">
                <span className="timestamp">[2024-01-15 14:23:04]</span>
                <span className="data-type">WARN</span>
                <span className="message">数据延迟检测: 延迟 150ms</span>
              </div>
            </div>
            <div className="stream-controls">
              <div className="stream-status">
                <span className="status-indicator active"></span>
                <span className="status-text">输出正常</span>
              </div>
              <div className="stream-stats">
                <span className="stat-item">速率: 2.3 msg/s</span>
                <span className="stat-item">总计: 1,247 条</span>
              </div>
            </div>
          </div>
        </div>

        <div className="event-page__section">
          <h3>关键指标</h3>
          <div className="metrics-grid">
            <div className="metric-item">
              <span className="metric-label">通道数</span>
              <span className="metric-value">10</span>
            </div>
            <div className="metric-item">
              <span className="metric-label">数据完整性</span>
              <span className="metric-value">99.8%</span>
            </div>
            <div className="metric-item">
              <span className="metric-label">错误数量</span>
              <span className="metric-value">3</span>
            </div>
            <div className="metric-item">
              <span className="metric-label">平均延迟</span>
              <span className="metric-value">125ms</span>
            </div>
          </div>
        </div>

        <div className="event-page__section">
          <h3>数据分析图表</h3>
          <div className="chart-placeholder">
            📈 {eventId} 事件数据趋势分析图表
          </div>
        </div>
      </div>
    </div>
  );
};

export default EventPage;
