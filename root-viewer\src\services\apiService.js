/**
 * API服务 - 处理与后端API的通信
 */

const API_BASE_URL = 'http://127.0.0.1:4523/m1/6925692-6641801-default';

/**
 * 获取DP基础数据
 * @returns {Promise<Object>} API响应数据
 */
export const fetchDPBasicData = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/message/DP_basic`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('获取DP基础数据失败:', error);
    throw error;
  }
};

/**
 * 格式化数据大小显示
 * @param {string} size - 数据大小字符串
 * @returns {string} 格式化后的大小
 */
export const formatDataSize = (size) => {
  if (!size) return '0MB';
  return size;
};

/**
 * 格式化布尔值显示
 * @param {boolean} value - 布尔值
 * @returns {string} 格式化后的字符串
 */
export const formatBooleanValue = (value) => {
  return value ? 'true' : 'false';
};

/**
 * 获取运行状态的CSS类名
 * @param {boolean} runState - 运行状态
 * @returns {string} CSS类名
 */
export const getRunStateClass = (runState) => {
  return runState ? 'system-status--normal' : 'system-status--abnormal';
};

/**
 * 获取运行状态的显示文本
 * @param {boolean} runState - 运行状态
 * @returns {string} 显示文本
 */
export const getRunStateText = (runState) => {
  return runState ? '运行状态' : '停止状态';
};
