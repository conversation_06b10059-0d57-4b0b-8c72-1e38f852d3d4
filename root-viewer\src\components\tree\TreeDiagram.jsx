import React from 'react';
import './TreeDiagram.css';

const TreeDiagram = ({ sidebarData }) => {
  // 如果没有数据，显示提示信息
  if (!sidebarData || Object.keys(sidebarData).length === 0) {
    return (
      <div className="tree-diagram">
        <div className="tree-no-data">
          <p>暂无数据，请点击"更新DP数据"按钮获取最新数据</p>
        </div>
      </div>
    );
  }

  // 渲染树节点
  const renderTreeNode = (nodeData, nodeKey, level = 0) => {
    const hasChildren = nodeData && typeof nodeData === 'object' && Object.keys(nodeData).length > 0;
    
    return (
      <div key={nodeKey} className={`tree-node tree-node--level-${level}`}>
        <div className="tree-node__content">
          <div className="tree-node__label">{nodeKey}</div>
          {hasChildren && <div className="tree-node__connector"></div>}
        </div>
        
        {hasChildren && (
          <div className="tree-node__children">
            {Object.entries(nodeData).map(([child<PERSON>ey, childData]) => 
              renderTreeNode(childData, childKey, level + 1)
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="tree-diagram">
      <div className="tree-root">
        <div className="tree-node tree-node--root">
          <div className="tree-node__content">
            <div className="tree-node__label tree-node__label--root">DP_fragment</div>
            <div className="tree-node__connector"></div>
          </div>
          
          <div className="tree-node__children">
            {Object.entries(sidebarData).map(([key, data]) => 
              renderTreeNode(data, key, 1)
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TreeDiagram;
