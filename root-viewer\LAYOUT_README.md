# 侧边导航布局组件

这是一个现代化的React侧边导航布局组件，提供了完整的管理系统界面框架。

## 功能特点

- ✨ **现代化设计**: 采用现代化的UI设计风格，美观大方
- 📱 **响应式布局**: 完美适配桌面端和移动端
- 🔄 **可折叠侧边栏**: 支持侧边栏的展开和收起
- 🎨 **主题化**: 易于自定义颜色和样式
- ⚡ **高性能**: 优化的组件结构，确保流畅的用户体验
- 🧩 **模块化**: 组件化设计，易于扩展和维护

## 组件结构

```
src/components/layout/
├── Layout.jsx          # 主布局组件
├── Layout.css          # 布局样式
├── Sidebar.jsx         # 侧边导航组件
└── Sidebar.css         # 侧边导航样式

src/components/pages/
├── ExamplePage.jsx     # 示例页面组件
└── ExamplePage.css     # 示例页面样式
```

## 使用方法

### 1. 基本使用

```jsx
import Layout from './components/layout/Layout';

function App() {
  return (
    <Layout>
      {/* 这里放置页面内容 */}
    </Layout>
  );
}
```

### 2. 自定义页面内容

```jsx
import Layout from './components/layout/Layout';
import ExamplePage from './components/pages/ExamplePage';

function App() {
  return (
    <Layout>
      <ExamplePage />
    </Layout>
  );
}
```

### 3. 添加新的菜单项

在 `Sidebar.jsx` 中修改 `menuItems` 数组：

```jsx
const menuItems = [
  { id: 'dashboard', label: '仪表板', icon: '📊' },
  { id: 'users', label: '用户管理', icon: '👥' },
  { id: 'products', label: '产品管理', icon: '📦' },
  // 添加新的菜单项
  { id: 'reports', label: '报表管理', icon: '📋' },
];
```

### 4. 添加对应的页面内容

在 `Layout.jsx` 中的 `DefaultContent` 组件中添加新页面：

```jsx
const content = {
  dashboard: <DashboardContent />,
  users: <UsersContent />,
  products: <ProductsContent />,
  // 添加新页面
  reports: <ReportsContent />,
};
```

## 样式自定义

### 1. 修改主题颜色

在 `Sidebar.css` 中修改CSS变量：

```css
.sidebar {
  background: linear-gradient(180deg, #your-color-1 0%, #your-color-2 100%);
}
```

### 2. 修改布局尺寸

在 `Layout.css` 和 `Sidebar.css` 中调整相关尺寸：

```css
.sidebar--open {
  width: 300px; /* 调整侧边栏宽度 */
}

.layout__header {
  height: 80px; /* 调整头部高度 */
}
```

## 响应式设计

布局组件已经内置了响应式设计：

- **桌面端** (>768px): 显示完整的侧边栏
- **移动端** (≤768px): 侧边栏可以完全隐藏或全屏显示

## 浏览器兼容性

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 开发建议

1. **组件化开发**: 为每个页面创建独立的组件
2. **状态管理**: 对于复杂应用，建议使用Redux或Context API
3. **路由管理**: 可以集成React Router进行页面路由管理
4. **图标库**: 建议使用React Icons或其他图标库替换emoji图标
5. **测试**: 为关键组件编写单元测试

## 扩展功能

可以考虑添加以下功能：

- 🔍 搜索功能
- 🌙 深色模式
- 🔔 通知系统
- 👤 用户权限管理
- 📊 数据可视化图表
- 🌐 国际化支持

## 启动项目

```bash
# 进入项目目录
cd root-viewer

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 构建项目

```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```
