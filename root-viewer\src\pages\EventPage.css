.event-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.event-page__header {
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.event-page__header h2 {
  margin: 0 0 8px 0;
  color: #1a1a1a;
  font-size: 28px;
  font-weight: 600;
}

.event-page__header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.event-page__content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.event-page__section {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.event-page__section h3 {
  margin: 0 0 16px 0;
  color: #1a1a1a;
  font-size: 20px;
  font-weight: 600;
}

.event-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.info-label {
  font-weight: 500;
  color: #495057;
}

.info-value {
  font-weight: 600;
  color: #212529;
}

.status-active {
  color: #28a745 !important;
}

.data-stream-container {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.data-stream-output {
  background: #000;
  color: #00ff00;
  padding: 16px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.4;
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.stream-line {
  margin-bottom: 4px;
}

.timestamp {
  color: #888;
}

.data-type {
  color: #00ffff;
  font-weight: bold;
  margin: 0 8px;
}

.message {
  color: #00ff00;
}

.stream-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stream-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #dc3545;
}

.status-indicator.active {
  background: #28a745;
}

.status-text {
  font-size: 14px;
  color: #495057;
}

.stream-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  font-size: 14px;
  color: #495057;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  text-align: center;
}

.metric-label {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #007bff;
}

.chart-placeholder {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 60px 20px;
  text-align: center;
  font-size: 18px;
  color: #6c757d;
}

@media (max-width: 768px) {
  .event-page {
    padding: 16px;
  }
  
  .event-info-grid {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stream-controls {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
